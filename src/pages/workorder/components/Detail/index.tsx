import { useEffect, useState } from 'react';
import { ArrowRight, Checklist } from '@nutui/icons-react-taro';
import {
  Button,
  Cell,
  Checkbox,
  CheckboxGroup,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Notify,
  Picker,
  Radio,
  TextArea,
  Uploader,
} from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { FileItem } from '@nutui/nutui-react-taro/dist/types/packages/uploader/file-item';
import { View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useReactive, useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { startWorkFlow } from '@/api/flow';
import { createWorkOrder, updateWorkOrder, workOrderInfo } from '@/api/workOrder';
import SearchSelect, { SearchItemProps } from '@/components/SearchSelect';
import TitleBar from '@/components/TitleBar';
import { WORK_ORDER_TYPE } from '@/enums';
import { useSafeStatusbar } from '@/hooks';
import { useAvailableProjectList } from '@/hooks/useAvailableProjectList';
import apiConfig from '@/services/api.config';
import { useLoginStore } from '@/store';
import { calcAvailableBottom, getRandomId, isValueNotEmpty } from '@/utils';

import './index.scss';

export const calculateDuration = (startTime: string, endTime: string) => {
  let workHours = 0;
  workHours = Math.abs(dayjs(endTime).diff(startTime, 'day') + 1) * 8;
  return workHours;
};

//工单类型
const workTypeList = WORK_ORDER_TYPE.map((item) => ({ ...item, text: item.label }));
const fileType = '.jpeg,.jpg,.png,image/jpeg,image/jpg,image/png,';
const fileSize = 1024 * 1024 * 20;

// 服务内容选项
const workOrderCategoryOptions = [
  '巡检',
  '安装部署',
  '技术保障',
  '故障处理',
  '升级',
  '性能优化',
  '技术交流',
  '技术咨询',
];

const Detail = () => {
  const navHeight = useSafeStatusbar();
  const aBottom = calcAvailableBottom();
  const bottomHeight = isValueNotEmpty(aBottom) ? (aBottom as number) : 0;
  const $instance = useRouter().params;
  const isEditPage = !!$instance.id;
  const { userInfo } = useLoginStore();
  const { availableProjectList, loading: availableProjectLoading } = useAvailableProjectList();
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<FileItem[]>([]);
  // const [fileNames, setFileNames] = useState<string[]>([]);

  //详情
  const getInfo = async () => {
    const res = await workOrderInfo({ req: { id: $instance.id! } });
    if (res.code !== 200) return;
    const { data } = res;
    if (data && Object.keys(data).length > 0) {
      const typeName = workTypeList.find((i) => i.value === data?.type)?.text;
      const startTime = dayjs(data.startTime).format('YYYY-MM-DD HH:mm');
      const endTime = dayjs(data.endTime).format('YYYY-MM-DD HH:mm');
      const fileUrl = data.wechatFileUrl?.map((i) => {
        const isPdf = i.includes('.pdf');
        return {
          url: isPdf ? '/public/images/pdf.svg' : i,
          uid: getRandomId(),
          type: 'image',
          message: '上传成功',
          status: 'success',
          name: i,
          path: isPdf ? '/public/images/pdf.svg' : i,
          responseText: {
            data: JSON.stringify({ data: i }),
          } as unknown as XMLHttpRequest['responseText'],
        };
      }) as FileItem[];
      setFileList(fileUrl);
      //去除值为空的key
      const Obj = Object.keys(data)
        .filter((key) => data[key] !== null && data[key] !== undefined)
        .reduce((acc, key) => ({ ...acc, [key]: data[key] }), {});
      const formData = {
        ...Obj,
        typeName: typeName,
        startTime: startTime,
        endTime: endTime,
        // 处理服务内容字段，如果是字符串则转换为数组
        workOrderCategory: data.workOrderCategory
          ? typeof data.workOrderCategory === 'string'
            ? data.workOrderCategory.split(',')
            : data.workOrderCategory
          : [],
      };
      form.setFieldsValue(formData);
    }
  };
  useEffect(() => {
    if (isEditPage) {
      getInfo();
    } else {
      const initValue = {
        projectType: 'SH',
        employeeName: userInfo?.username,
        employeeNumber: userInfo?.employeeNumber,
      };
      form.setFieldsValue(initValue);
    }
  }, [isEditPage]);
  // 弹窗开关集合
  const visibleCollection = useReactive({
    type: false,
    startTime: false,
    endTime: false,
    projectNumber: false,
    notifyVisible: false,
    notifyMassage: '请先选择开始日期和结束日期',
  });
  //新建
  const { run: AddForm, loading: addLoading } = useRequest((value) => createWorkOrder(value), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code === 200) {
        Taro.showToast({ title: '提交成功', icon: 'success' });
        onNavBack();
      }
    },
  });

  //编辑
  const { run: UpdateForm, loading: updateLoading } = useRequest(
    (value) => updateWorkOrder(value),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code === 200) {
          Taro.showToast({ title: '提交成功', icon: 'success' });
          onNavBack();
        }
      },
    },
  );

  //保存并提交
  const { run: submitForm, loading: submitLoading } = useRequest((value) => startWorkFlow(value), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code === 200) {
        Taro.showToast({ title: '提交成功', icon: 'success' });
        onNavBack();
      }
    },
  });

  const submitFailed = () => {
    visibleCollection.notifyMassage = '请完善必填项！';
    visibleCollection.notifyVisible = true;
  };

  const formatter = (type: string, option: PickerOption) => {
    switch (type) {
      case 'year':
        option.text += '年';
        break;
      case 'month':
        option.text += '月';
        break;
      case 'day':
        option.text += '日';
        break;
      case 'hour':
        option.text += '时';
        break;
      case 'minute':
        option.text += '分';
        break;
      default:
        option.text += '';
    }
    return option;
  };

  const onNavBack = () => {
    Taro.eventCenter.trigger('WORK_ORDER_REFRESH');
    Taro.navigateBack();
  };

  return (
    <View className='detail'>
      <TitleBar title='工单' onNavBack={onNavBack} />

      <View className='detail-container' style={{ marginTop: navHeight + 20 }}>
        <Form
          form={form}
          className='detail-container_form'
          labelPosition='left'
          onFinishFailed={() => submitFailed()}
          footer={
            <View className='detail-main_actions' style={{ height: 30 + bottomHeight + 'px' }}>
              <Button
                loading={addLoading || updateLoading}
                shape='square'
                fill='none'
                className='detail-main_actions_button'
                onClick={() => {
                  const obj = form.getFieldsValue(true);
                  const objList = Object.keys(obj);
                  const result = [
                    'employeeNumber',
                    'employeeName',
                    'endTime',
                    'projectId',
                    'projectName',
                    'projectNumber',
                    'projectType',
                    'startTime',
                    'type',
                    'workHours',
                    'describe',
                    'content',
                    'workOrderCategory',
                  ].every((item) => objList.includes(item));
                  const filesUrl = fileList.map((i) => {
                    const data = i.responseText as XMLHttpRequest['responseText'];
                    //@ts-ignore
                    const res = JSON.parse(data?.data!);
                    return res?.data;
                  });

                  if (result === true) {
                    const { workHours, startTime, endTime, workOrderCategory } = obj;
                    const formData = {
                      ...obj,
                      workHours: String(workHours),
                      startTime: `${startTime}:00`,
                      endTime: `${endTime}:00`,
                      fileUrl: filesUrl,
                      wechatFileUrl: filesUrl,
                      // 将服务内容数组转换为逗号分隔的字符串
                      workOrderCategory: Array.isArray(workOrderCategory)
                        ? workOrderCategory.join(',')
                        : workOrderCategory || '',
                    };
                    if (isEditPage) {
                      UpdateForm({ ...formData, id: $instance.id });
                    } else {
                      AddForm({ ...formData });
                    }
                  } else {
                    submitFailed();
                  }
                }}
              >
                保存
              </Button>
              <View className='detail-main_actions_divider' />
              {isEditPage && (
                <Button
                  loading={submitLoading}
                  onClick={() => {
                    const obj = form.getFieldsValue(true);
                    const objList = Object.keys(obj);
                    const result = [
                      'id',
                      'projectId',
                      'projectNumber',
                      'projectName',
                      'projectType',
                      'startTime',
                      'endTime',
                      'type',
                      'employeeNumber',
                      'employeeName',
                      'workHours',
                      'describe',
                      'content',
                      'workOrderCategory',
                    ].every((item) => objList.includes(item));
                    const filesUrl = fileList.map((i) => {
                      const data = i.responseText as XMLHttpRequest['responseText'];
                      //@ts-ignore
                      const res = JSON.parse(data?.data!);
                      return res?.data;
                    });

                    if (result === true) {
                      const { workHours, startTime, endTime, workOrderCategory } = obj;
                      const formData = {
                        ...obj,
                        workHours: String(workHours),
                        startTime: `${startTime}:00`,
                        endTime: `${endTime}:00`,
                        fileUrl: filesUrl,
                        wechatFileUrl: filesUrl,
                        // 将服务内容数组转换为逗号分隔的字符串
                        workOrderCategory: Array.isArray(workOrderCategory)
                          ? workOrderCategory.join(',')
                          : workOrderCategory || '',
                      };
                      UpdateForm({ ...formData, id: $instance.id });
                      submitForm({ businessKey: $instance.id, type: 'WORK_ORDER_APPROVAL' });
                    } else {
                      submitFailed();
                    }
                  }}
                  shape='square'
                  fill='none'
                  className='detail-main_actions_button'
                >
                  保存并提交
                </Button>
              )}
            </View>
          }
        >
          <Form.Item label='工单类型' name='type' rules={[{ required: true, message: '请选择' }]}>
            <>
              <Cell
                className='detail-container_form_cell'
                title={form.getFieldValue('typeName') ?? '请选择'}
                align='center'
                onClick={() => {
                  visibleCollection.type = true;
                }}
                extra={<ArrowRight size={15} />}
              />
              <Picker
                options={workTypeList}
                visible={visibleCollection.type}
                onClose={() => (visibleCollection.type = false)}
                onConfirm={(options) => {
                  if (options?.length > 0) {
                    form.setFieldsValue({ type: options[0].value, typeName: options[0].text });
                  }
                }}
              />
            </>
          </Form.Item>
          <Form.Item
            label='项目类型'
            name='projectType'
            rules={[{ required: true, message: '请选择' }]}
          >
            <Radio.Group
              onChange={() => {
                form.setFieldsValue({ projectNumber: null, projectName: '', projectId: '' });
              }}
            >
              {[
                { label: '售后项目', value: 'SH' },
                { label: '售前项目', value: 'SQ' },
              ].map((i) => {
                return (
                  <Radio
                    key={i.value}
                    value={i.value}
                    icon={<Checklist />}
                    activeIcon={<Checklist />}
                  >
                    {i.label}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label='项目编号'
            name='projectNumber'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  if (availableProjectLoading) {
                    return;
                  }
                  if (
                    availableProjectList.filter(
                      (i: Record<string, any>) =>
                        i.projectClassify === form.getFieldValue('projectType'),
                    ).length <= 0
                  ) {
                    return;
                  }
                  visibleCollection.projectNumber = true;
                }}
                className='detail-container_form_cell'
                title={
                  availableProjectList.filter(
                    (i: Record<string, any>) =>
                      i.projectClassify === form.getFieldValue('projectType'),
                  ).length > 0
                    ? availableProjectLoading
                      ? '数据准备中'
                      : form.getFieldValue('projectNumber') ?? '请选择'
                    : '暂无数据'
                }
                extra={<ArrowRight size={15} />}
                align='center'
              />
              {availableProjectList?.length && (
                <SearchSelect
                  originList={
                    availableProjectList.filter(
                      (i: Record<string, any>) =>
                        i.projectClassify === form.getFieldValue('projectType'),
                    ) as SearchItemProps[]
                  }
                  visible={visibleCollection.projectNumber}
                  close={() => {
                    visibleCollection.projectNumber = false;
                  }}
                  done={(options: PickerOption[] & API.ProBaseInfoResp[]) => {
                    if (options?.length) {
                      form.setFieldsValue({
                        projectNumber: options[0]?.value,
                        projectName: options[0]?.projectName,
                        projectId: options[0]?.id,
                      });
                    }
                  }}
                />
              )}
            </>
            {/* <>
              <Cell
                onClick={() => {
                  if (!isEditPage && availableProjectLoading) {
                    return;
                  }
                  visibleCollection.projectNumber = true;
                }}
                className='detail-container_form_cell'
                title={
                  availableProjectLoading
                    ? '数据准备中'
                    : form.getFieldValue('projectNumber') ?? '请选择'
                }
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <Picker
                visible={visibleCollection.projectNumber}
                options={
                  availableProjectList.filter(
                    (i: Record<string, any>) =>
                      i.projectClassify === form.getFieldValue('projectType'),
                  ) as PickerOption[]
                }
                onClose={() => {
                  visibleCollection.projectNumber = false;
                }}
                onConfirm={(options: (PickerOption & API.ProBaseInfoResp)[]) => {
                  if (options?.length) {
                    form.setFieldsValue({
                      projectNumber: options[0]?.value,
                      projectName: options[0]?.projectName,
                      projectId: options[0]?.id,
                    });
                  }
                }}
              />
            </> */}
          </Form.Item>
          <Form.Item
            label='项目名称'
            name='projectName'
            rules={[{ required: true, message: '请输入' }]}
          >
            <Input placeholder='请输入' type='text' disabled />
          </Form.Item>
          <Form.Item
            label='工程师工号'
            name='employeeNumber'
            rules={[{ required: true, message: '请输入' }]}
          >
            <Input placeholder='请输入' type='text' disabled />
          </Form.Item>
          <Form.Item
            label='工程师姓名'
            name='employeeName'
            rules={[{ required: true, message: '请输入' }]}
          >
            <Input placeholder='请输入' type='text' disabled />
          </Form.Item>
          <Form.Item
            label='开始时间'
            name='startTime'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  visibleCollection.startTime = true;
                }}
                className='detail-container_form_cell'
                title={form.getFieldValue('startTime') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <DatePicker
                type='datetime'
                visible={visibleCollection.startTime}
                defaultValue={new Date()}
                formatter={formatter}
                onClose={() => (visibleCollection.startTime = false)}
                onConfirm={(_, values) => {
                  const startTime = values.slice(0, 3).join('-') + ' ' + values.slice(3).join(':');
                  const endTime = form.getFieldValue('endTime');
                  if (dayjs(startTime).isAfter(endTime) || dayjs(startTime).isSame(endTime)) {
                    visibleCollection.notifyMassage = '结束时间必须大于开始时间';
                    visibleCollection.notifyVisible = true;
                  }
                  if (endTime) {
                    form.setFieldsValue({
                      workHours: calculateDuration(startTime, endTime),
                    });
                  }

                  form.setFieldsValue({
                    startTime: startTime,
                  });
                }}
              />
            </>
          </Form.Item>
          <Form.Item
            label='结束时间'
            name='endTime'
            trigger='onConfirm'
            rules={[{ required: true, message: '请选择' }]}
          >
            <>
              <Cell
                onClick={() => {
                  visibleCollection.endTime = true;
                }}
                className='detail-container_form_cell'
                title={form.getFieldValue('endTime') ?? '请选择'}
                extra={<ArrowRight size={15} />}
                align='center'
              />
              <DatePicker
                type='datetime'
                visible={visibleCollection.endTime}
                defaultValue={new Date()}
                formatter={formatter}
                onClose={() => (visibleCollection.endTime = false)}
                onConfirm={(_, values) => {
                  const startTime = form.getFieldValue('startTime');
                  const endTime = values.slice(0, 3).join('-') + ' ' + values.slice(3).join(':');
                  if (dayjs(startTime).isAfter(endTime) || dayjs(startTime).isSame(endTime)) {
                    visibleCollection.notifyMassage = '结束时间必须大于开始时间';
                    visibleCollection.notifyVisible = true;
                  }
                  if (startTime) {
                    form.setFieldsValue({
                      workHours: calculateDuration(startTime, endTime),
                    });
                  }
                  form.setFieldsValue({
                    endTime: endTime,
                  });
                }}
              />
            </>
          </Form.Item>
          <Form.Item label='联系人姓名' name='contactName'>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item label='联系人电话' name='contactPhone'>
            <Input placeholder='请输入' type='text' />
          </Form.Item>
          <Form.Item
            label='服务内容'
            name='workOrderCategory'
            rules={[{ required: true, message: '请选择' }]}
          >
            <CheckboxGroup
              onChange={(values) => {
                form.setFieldsValue({ workOrderCategory: values });
              }}
            >
              {workOrderCategoryOptions.map((option) => (
                <Checkbox key={option} value={option}>
                  {option}
                </Checkbox>
              ))}
            </CheckboxGroup>
          </Form.Item>
          <Form.Item label='工时' name='workHours' rules={[{ required: true, message: '请输入' }]}>
            <InputNumber
              min={0}
              max={999999999999999}
              digits={2}
              formatter={(value) => `${value}时`}
            />
          </Form.Item>
          <Form.Item
            label='事件描述'
            name='describe'
            rules={[{ required: true, message: '请输入' }]}
          >
            <TextArea showCount placeholder='请输入' />
          </Form.Item>
          <Form.Item
            label='工作内容'
            name='content'
            rules={[{ required: true, message: '请输入' }]}
          >
            <TextArea showCount placeholder='请输入' />
          </Form.Item>
          <Form.Item label='下一步计划' name='nextPlan'>
            <TextArea showCount placeholder='请输入' />
          </Form.Item>
          <Form.Item
            label='工单附件(目前仅支持图片)'
            name='fileUrl'
            style={{
              display: 'flex',
              alignItems: 'initial',
              flexDirection: 'column',
            }}
          >
            <>
              <Uploader
                style={{ padding: '20px 0 40px 0' }}
                previewType='picture'
                accept={fileType}
                multiple
                maxCount={5}
                maxFileSize={fileSize}
                url={`${apiConfig.baseUrl}/api/common/file/upload`}
                headers={{
                  Authorization: Taro.getStorageSync('RKLINK_OA_TOKEN')!,
                }}
                method='post'
                value={fileList}
                onChange={(files) => {
                  setFileList(files);
                }}
                onDelete={(file) => {
                  const filteredFiles = fileList.filter((i) => i.uid !== file.uid);
                  setFileList(filteredFiles);
                  return false;
                }}
                disabled={fileList?.length >= 5}
              />
            </>
          </Form.Item>
        </Form>
      </View>

      <Notify
        style={{ marginTop: navHeight }}
        visible={visibleCollection.notifyVisible}
        type='danger'
        onClose={() => {
          visibleCollection.notifyVisible = false;
        }}
      >
        {visibleCollection.notifyMassage}
      </Notify>
    </View>
  );
};

export default Detail;
