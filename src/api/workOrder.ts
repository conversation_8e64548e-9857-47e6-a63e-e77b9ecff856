// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 新建工单 POST /api/work-order/work-order/work-order-create */
export async function createWorkOrder(
  body: API.WorkOrderInsertReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/work-order/work-order/work-order-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除工单 POST /api/work-order/work-order/work-order-del */
export async function delWorkOrder(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/work-order/work-order/work-order-del', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 工单历史联系人 GET /api/work-order/work-order/work-order-historical-contact */
export async function getHistoricalContact(options?: { [key: string]: any }) {
  return request<API.ResultListHistoricalContactsResp>(
    '/api/work-order/work-order/work-order-historical-contact',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 工单信息 GET /api/work-order/work-order/work-order-info */
export async function workOrderInfo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.workOrderInfoParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultWorkOrderInfoResp>('/api/work-order/work-order/work-order-info', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 工单分页 POST /api/work-order/work-order/work-order-page */
export async function workOrderPage(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageWorkOrderPageResp>('/api/work-order/work-order/work-order-page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新工单 POST /api/work-order/work-order/work-order-update */
export async function updateWorkOrder(
  body: API.WorkOrderUpdateReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/work-order/work-order/work-order-update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
