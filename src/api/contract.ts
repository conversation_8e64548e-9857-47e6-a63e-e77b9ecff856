// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 申请付款 申请付款 POST /api/contract/applicationPay */
export async function applicationPay(body: API.PaymentAppReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/applicationPay', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 申请开票，确认开票 申请开票，确认开票 POST /api/contract/applicationTicket */
export async function applicationTicket(body: API.InvoicingReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/applicationTicket', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据项目id查询合同收付款信息 POST /api/contract/contract-receipts-and-payments */
export async function contractReceiptsAndPayments(
  body: API.IdReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultContractReceiptsAndPaymentsResp>(
    '/api/contract/contract-receipts-and-payments',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 创建合作协议 创建合作协议 POST /api/contract/createCoopAgree */
export async function createCoopAgree(body: API.CoopAgreeReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/createCoopAgree', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建内部合同 创建内部合同 POST /api/contract/createInnerContract */
export async function createInnerContract(body: API.InnerConReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/createInnerContract', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建主合同 创建主合同 POST /api/contract/createMainContract */
export async function createMainContract(
  body: API.MainContractReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/contract/createMainContract', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建采购合同 创建采购合同 POST /api/contract/createPurContract */
export async function createPurContract(
  body: API.PurContractReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/contract/createPurContract', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键删除合作协议 根据主键删除合作协议 DELETE /api/contract/deleteCoopAgrById */
export async function deleteCoopAgrById(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/deleteCoopAgrById', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键删除内部下单合同 根据主键删除内部下单合同 DELETE /api/contract/deleteInConById */
export async function deleteInConById(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/deleteInConById', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键删除主合同 根据主键删除主合同 DELETE /api/contract/deleteMainConById */
export async function deleteMainConById(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/deleteMainConById', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键删除采购合同 根据主键删除采购合同 DELETE /api/contract/deletePurConById */
export async function deletePurConById(body: API.IdsReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/deletePurConById', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出内部下单合同 GET /api/contract/export-inner */
export async function exportInner(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.exportInnerParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/contract/export-inner', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 导出主合同 GET /api/contract/export-main */
export async function exportMain(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.exportMainParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/contract/export-main', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 导出采购合同 GET /api/contract/export-pur */
export async function exportPur(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.exportPurParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/contract/export-pur', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 根据合同id查询项目 POST /api/contract/find-project-by-contract */
export async function findProjectByContract(
  body: API.FindProjectByContractReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultListProBaseInfoResp>('/api/contract/find-project-by-contract', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主键查询合作协议 根据主键查询合作协议 GET /api/contract/getCoopAgreeById */
export async function getCoopAgreeById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCoopAgreeByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultCoopAgreeResp>('/api/contract/getCoopAgreeById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 根据主键查询内部合同 根据主键查询内部合同 GET /api/contract/getInnerConById */
export async function getInnerConById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getInnerConByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultInnerContractResp>('/api/contract/getInnerConById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 根据主键查询主合同信息 GET /api/contract/getMainConById */
export async function getMainConById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMainConByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultMainContractResp>('/api/contract/getMainConById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 根据主键查询采购合同信息 根据主键查询采购合同信息 GET /api/contract/getPurConById */
export async function getPurConById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getPurConByIdParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultPurContractResp>('/api/contract/getPurConById', {
    method: 'GET',
    params: {
      ...params,
      idReq: undefined,
      ...params['idReq'],
    },
    ...(options || {}),
  });
}

/** 根据采购合同id查询主合同差额相关信息 POST /api/contract/main-contract-difference */
export async function mainContractDifference(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultMainContractDifferenceResp>('/api/contract/main-contract-difference', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 合同付款额外信息 POST /api/contract/main-contract-extra-info */
export async function getMainContractExtraInfo(
  body: API.MainContractExtraReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultMainContractExtraResp>('/api/contract/main-contract-extra-info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 可用的主合同列表 审批通过的主合同列表 GET /api/contract/main-contract-list */
export async function mainContractList(options?: { [key: string]: any }) {
  return request<API.ResultListMainContractListResp>('/api/contract/main-contract-list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 主合同可用售前项目列表 主合同可用售前项目列表 POST /api/contract/main-project-list-sq */
export async function mainProjectListSq(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultListProjectNameIdResp>('/api/contract/main-project-list-sq', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 主合同关联售前成本明细 POST /api/contract/main-relevancy-sq-fees-details */
export async function mainRelevancySqFeesDetails(
  body: API.RelevancySqFeesReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/contract/main-relevancy-sq-fees-details', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据主合同id查询售前成本明细 POST /api/contract/main-sq-fees-details */
export async function mainSqFeesDetails(body: API.IdReq, options?: { [key: string]: any }) {
  return request<API.ResultProjectSqFeesDetailsResp>('/api/contract/main-sq-fees-details', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询合作协议 分页查询合作协议 POST /api/contract/pageCoopAgree */
export async function pageCoopAgree(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageCoopAgreeResp>('/api/contract/pageCoopAgree', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询内部合同 分页查询内部合同 POST /api/contract/pageInnerCon */
export async function pageInnerContract(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageInnerConInfoResp>('/api/contract/pageInnerCon', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询主合同信息 分页查询主合同信息 POST /api/contract/pageMainContract */
export async function pageMainContract(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPageMainContractInfoResp>('/api/contract/pageMainContract', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询采购合同 分页查询采购合同 POST /api/contract/pagePurContract */
export async function pagePurContract(body: API.PageReq, options?: { [key: string]: any }) {
  return request<API.ResultPagePurContractInfoResp>('/api/contract/pagePurContract', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新合作协议 更新合作协议 PUT /api/contract/updateCoopAgree */
export async function updateCoopAgree(body: API.CoopAgreeReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/updateCoopAgree', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新内部合同 更新内部合同 PUT /api/contract/updateInnerContract */
export async function updateInnerContract(body: API.InnerConReq, options?: { [key: string]: any }) {
  return request<API.ResultObject>('/api/contract/updateInnerContract', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改主合同 修改主合同 PUT /api/contract/updateMainContract */
export async function updateMainContract(
  body: API.MainContractReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/contract/updateMainContract', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改采购合同 修改采购合同 PUT /api/contract/updatePurContract */
export async function updatePurContract(
  body: API.PurContractReq,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/contract/updatePurContract', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
