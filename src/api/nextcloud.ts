// @ts-ignore
/* eslint-disable */
import request from '@/services/api';

/** 目录 POST /api/nextcloud/directory */
export async function getFiles(body: API.FilePathReq, options?: { [key: string]: any }) {
  return request<API.ResultListDavFileVO>('/api/nextcloud/directory', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 下载 GET /api/nextcloud/download */
export async function download(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.downloadParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/nextcloud/download', {
    method: 'GET',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}

/** 上传文件 POST /api/nextcloud/upload */
export async function upload(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.uploadParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject>('/api/nextcloud/upload', {
    method: 'POST',
    params: {
      ...params,
      req: undefined,
      ...params['req'],
    },
    ...(options || {}),
  });
}
